import { derive, provide } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { getServerConfig } from "@/base/backend/config";
import { rpc } from "@/base/backend/routes";
import { serve } from "bun";

import { shape } from "@/mod/backend/shape";
import frontend from "@/mod/frontend/index.html";
import { userManagementRoutes } from "@/mod/backend/user-management-routes";
import { authRoutes } from "@/mod/backend/auth-routes";
import { initializeAuth } from "@/mod/backend/seed-admin";

const serverConfig = provide(() => getServerConfig());
const server = derive(
  [serverConfig, logger('server'), rpc, shape],
  async ([config, sLogger, rpc, shape], ctl) => {
    sLogger.info(`Starting server on ${config.host}:${config.port}...`);

    // Initialize authentication system
    try {
      await initializeAuth();
      sLogger.info('Authentication system initialized');
    } catch (error) {
      sLogger.error('Failed to initialize authentication:', error);
    }

    const server = serve({
      port: config.port,
      hostname: config.host,
      development: true,
      routes: {
        "/": frontend,
        "/rpc": rpc,
        "/shape": shape,
        ...userManagementRoutes,
        ...authRoutes
      }
    })

    ctl.cleanup(async () => {
      sLogger.info("Shutting down server...");
      await server.stop();
    })

    await new Promise<void>(() => {})
  }
)

export default server;