# 🚀 Tini Melon - Startup Commands Reference

## 📋 Command Summary

### 🎯 **Primary Startup Command (Recommended)**
```bash
# Start development server with user management features
bun run script dev-server
```
**Access:** http://localhost:3000

---

## 🔧 All Available Commands

### **Development Commands**
```bash
# Development server (no database required)
bun run script dev-server

# Full server with database integration
bun run script server
```

### **Database Commands**
```bash
# Start database services (PostgreSQL + Electric)
task compose:up

# Stop database services
task compose:down

# Update database schema
task drizzle:up

# Seed database with sample data
bun run script db seed

# Reset database
bun run script db reset
```

### **Setup Commands**
```bash
# Install dependencies
bun install

# Copy environment file
cp .env .env.local
```

### **Utility Commands**
```bash
# Type checking
bun run tsc --noEmit

# View available tasks
task --list

# Check Docker services
docker compose ps
```

---

## 🎯 Startup Workflows

### **Workflow 1: Quick Development (User Management Focus)**
```bash
# 1. Install dependencies
bun install

# 2. Start development server
bun run script dev-server

# 3. Open browser
open http://localhost:3000
```
**Features Available:**
- ✅ Full user management system
- ✅ In-memory data storage
- ✅ RESTful API endpoints
- ✅ Real-time form validation
- ✅ Toast notifications
- ✅ Responsive UI with DaisyUI

### **Workflow 2: Full Stack Development (All Features)**
```bash
# 1. Install dependencies
bun install

# 2. Start database services
task compose:up

# 3. Setup database schema
task drizzle:up

# 4. Seed database (optional)
bun run script db seed

# 5. Start full server
bun run script server

# 6. Open browser
open http://localhost:3000
```
**Features Available:**
- ✅ All user management features
- ✅ Todo management system
- ✅ Persistent database storage
- ✅ Real-time data synchronization
- ✅ RPC and Shape APIs
- ✅ Electric SQL integration

---

## 🌐 Available Endpoints

### **Frontend URLs**
- **Main Application**: http://localhost:3000
- **User Management**: http://localhost:3000 (User Management tab)
- **Todo Management**: http://localhost:3000 (Todo Management tab)

### **API Endpoints**

#### **User Management API** (Available in both modes)
- `GET /api/users` - List users with pagination/filtering
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update existing user
- `PATCH /api/users/:id/status` - Toggle user status
- `DELETE /api/users/:id` - Delete user (soft delete)

#### **Full Stack APIs** (Only in full server mode)
- `POST /rpc` - RPC endpoint for complex operations
- `GET /shape` - Shape endpoint for real-time data

---

## 🧪 Testing Commands

### **API Testing**
```bash
# Test user creation
curl -X POST "http://localhost:3000/api/users" \
  -H "Content-Type: application/json" \
  -d '{"firstName":"Test","lastName":"User","email":"<EMAIL>","role":"user","status":"active"}'

# Test user listing
curl -X GET "http://localhost:3000/api/users"

# Test user filtering
curl -X GET "http://localhost:3000/api/users?search=john&role=admin"

# Test pagination
curl -X GET "http://localhost:3000/api/users?page=1&limit=5"

# Test user status toggle
curl -X PATCH "http://localhost:3000/api/users/1/status"
```

### **Health Check**
```bash
# Check if server is running
curl -I http://localhost:3000

# Check API availability
curl -X GET "http://localhost:3000/api/users" | jq '.pagination'
```

---

## 🔧 Configuration

### **Environment Variables**
```bash
# Server Configuration
SERVER_PORT=3000                  # Default: 3000
SERVER_HOST=0.0.0.0              # Default: 0.0.0.0

# Database Configuration (for full mode)
POSTGRES_DB=postgres              # Database name
POSTGRES_USER=postgres            # Database user
POSTGRES_PASSWORD=password        # Database password
POSTGRES_PORT=54321              # Database port

# Electric Configuration (for real-time features)
ELECTRIC_HOST=localhost           # Electric host
ELECTRIC_PORT=55432              # Electric port
```

### **Port Configuration**
```bash
# Change default port
echo "SERVER_PORT=3001" >> .env

# Or set temporarily
SERVER_PORT=3001 bun run script dev-server
```

---

## 🚨 Troubleshooting

### **Common Issues & Solutions**

#### **Port Already in Use**
```bash
# Find and kill process
lsof -ti:3000 | xargs kill -9

# Or change port
SERVER_PORT=3001 bun run script dev-server
```

#### **Dependencies Issues**
```bash
# Clear cache and reinstall
rm -rf node_modules bun.lock
bun install
```

#### **Database Connection Issues**
```bash
# Check Docker services
docker compose ps

# Restart services
task compose:down
task compose:up

# Check logs
docker compose logs postgres
docker compose logs electric
```

#### **Build Errors**
```bash
# Check TypeScript
bun run tsc --noEmit

# Clear dist folder
rm -rf dist

# Restart server
bun run script dev-server
```

---

## 📊 Server Status Indicators

### **Successful Startup Messages**
```
✅ Development Server:
ℹ Starting development server on 0.0.0.0:3000...
ℹ 🚀 Development server running at http://0.0.0.0:3000
ℹ 📱 User Management API available at /api/users

✅ Full Server:
ℹ Starting server on 0.0.0.0:3000...
ℹ Server running at http://0.0.0.0:3000
```

### **Database Service Status**
```bash
# Check services
task compose:up

# Expected output:
✅ postgres container running on port 54321
✅ electric container running on port 55432
```

---

## 🎯 Quick Commands Cheat Sheet

```bash
# Essential commands for daily development
bun install                    # Install dependencies
bun run script dev-server     # Start development server
task compose:up               # Start database (if needed)
task compose:down             # Stop database
curl -X GET localhost:3000/api/users  # Test API

# Database management
task drizzle:up               # Update schema
bun run script db seed        # Add sample data
bun run script db reset       # Reset database

# Troubleshooting
lsof -ti:3000 | xargs kill -9  # Kill port 3000
rm -rf node_modules bun.lock && bun install  # Fresh install
```

---

## 🎉 Success Verification

After running `bun run script dev-server`, verify everything works:

1. **✅ Server Running**: See startup messages in terminal
2. **✅ Frontend Loading**: Open http://localhost:3000
3. **✅ API Working**: Run `curl -X GET localhost:3000/api/users`
4. **✅ User Management**: Click "User Management" tab
5. **✅ Create User**: Click "Add User" and test form
6. **✅ Responsive**: Resize browser window to test mobile view

**🎊 If all checks pass, you're ready to develop!**
