import React, { useState } from 'react';
import { Resolves } from '@pumped-fn/react';
import { app } from '../../pumped.auth';
import AuthGuard from './AuthGuard';
import TodoApp from '../TodoApp';
import UserManagementApp from '../user-management/UserManagementApp';

type AppView = 'todos' | 'users';

const AuthApp: React.FC = () => {
  const [currentView, setCurrentView] = useState<AppView>('todos');

  console.log('[AUTH APP] Rendering AuthApp component');

  return (
    <Resolves e={[
      app.state.reactive,
      app.isAuthenticated,
      app.isAdmin,
      app.currentUser,
      app.actions
    ]}>
      {([authState, isAuthenticated, isAdmin, currentUser, authActions]) => {
        console.log('[AUTH APP] Resolves callback with state:', {
          loading: authState.loading,
          user: authState.user,
          isAuthenticated
        });

        const handleSignOut = async () => {
          try {
            await authActions.signOut();
          } catch (error) {
            console.error('Sign out failed:', error);
          }
        };

        return (
          <AuthGuard>
      <div className="min-h-screen bg-base-200">
        {/* Navigation Bar */}
        <div className="navbar bg-base-100 shadow-lg">
          <div className="navbar-start">
            <div className="dropdown">
              <div tabIndex={0} role="button" className="btn btn-ghost lg:hidden">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h8m-8 6h16" />
                </svg>
              </div>
              <ul tabIndex={0} className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                <li>
                  <button
                    onClick={() => setCurrentView('todos')}
                    className={currentView === 'todos' ? 'active' : ''}
                  >
                    📝 Todos
                  </button>
                </li>
                {isAdmin && (
                  <li>
                    <button
                      onClick={() => setCurrentView('users')}
                      className={currentView === 'users' ? 'active' : ''}
                    >
                      👥 Users
                    </button>
                  </li>
                )}
              </ul>
            </div>
            <a className="btn btn-ghost text-xl">🍈 Tini Melon</a>
          </div>

          <div className="navbar-center hidden lg:flex">
            <ul className="menu menu-horizontal px-1">
              <li>
                <button
                  onClick={() => setCurrentView('todos')}
                  className={`btn btn-ghost ${currentView === 'todos' ? 'btn-active' : ''}`}
                >
                  📝 Todos
                </button>
              </li>
              {isAdmin && (
                <li>
                  <button
                    onClick={() => setCurrentView('users')}
                    className={`btn btn-ghost ${currentView === 'users' ? 'btn-active' : ''}`}
                  >
                    👥 Users
                  </button>
                </li>
              )}
            </ul>
          </div>

          <div className="navbar-end">
            {/* User Info */}
            <div className="flex items-center gap-2 mr-4">
              <div className="text-sm">
                <div className="font-medium">{currentUser?.name}</div>
                <div className="text-xs opacity-70">
                  {currentUser?.role === 'admin' ? '👑 Admin' : '👤 User'}
                </div>
              </div>
            </div>

            {/* User Menu */}
            <div className="dropdown dropdown-end">
              <div tabIndex={0} role="button" className="btn btn-ghost btn-circle avatar">
                <div className="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
                  {currentUser?.name?.charAt(0).toUpperCase() || '?'}
                </div>
              </div>
              <ul tabIndex={0} className="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box w-52">
                <li className="menu-title">
                  <span>{currentUser?.email}</span>
                </li>
                <li><a>Profile</a></li>
                <li><a>Settings</a></li>
                <li>
                  <button onClick={handleSignOut} className="text-error">
                    Sign Out
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <main className="min-h-[calc(100vh-4rem)]">
          {currentView === 'todos' && <TodoApp />}
          {currentView === 'users' && isAdmin && (
            <AuthGuard requireRole="admin">
              <UserManagementApp />
            </AuthGuard>
          )}
        </main>
        </div>
      </AuthGuard>
    );
      }}
    </Resolves>
  );
};

export default AuthApp;
