import React, { useEffect, useState } from 'react';
import { Resolves } from '@pumped-fn/react';
import { app } from '../../pumped.auth';
import LoginPage from './LoginPage';
import RegisterPage from './RegisterPage';

interface AuthGuardProps {
  children: React.ReactNode;
  requireRole?: string; // Optional role requirement
  fallback?: React.ReactNode; // Custom fallback component
}

type AuthView = 'login' | 'register';

const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireRole,
  fallback
}) => {
  const [currentView, setCurrentView] = useState<AuthView>('login');
  const [hasTriggeredInit, setHasTriggeredInit] = useState(false);

  return (
    <Resolves e={[
      app.state.reactive,
      app.isAuthenticated,
      app.isAdmin,
      app.actions,
      app.initializeAction
    ]}>
      {([authState, isAuthenticated, isAdmin, authActions, initAction]) => {
        console.log('[AUTH GUARD] Rendering with state:', {
          loading: authState.loading,
          user: authState.user,
          session: authState.session,
          isAuthenticated
        });

        // Use useEffect to trigger initialization (avoid setState during render)
        useEffect(() => {
          if (!hasTriggeredInit) {
            console.log('[AUTH GUARD] Triggering initialization...');
            setHasTriggeredInit(true);
            initAction.initialize();
          }
        }, [hasTriggeredInit, initAction]);

        // Show loading while initializing
        if (authState.loading) {
          return (
            <div className="min-h-screen bg-base-200 flex items-center justify-center">
              <div className="text-center">
                <div className="loading loading-spinner loading-lg"></div>
                <p className="mt-4 text-base-content">Loading...</p>
              </div>
            </div>
          );
        }

        // Check if user is authenticated
        if (!isAuthenticated) {
          if (fallback) {
            return <>{fallback}</>;
          }

          // Show login/register forms
          const handleAuthSuccess = () => {
            // Auth state will update automatically, component will re-render
          };

          if (currentView === 'login') {
            return (
              <LoginPage
                onSuccess={handleAuthSuccess}
                onSwitchToRegister={() => setCurrentView('register')}
              />
            );
          } else {
            return (
              <RegisterPage
                onSuccess={handleAuthSuccess}
                onSwitchToLogin={() => setCurrentView('login')}
              />
            );
          }
        }

        // Check role requirements
        if (requireRole) {
          const hasRequiredRole = checkUserRole(authState.user, requireRole);

          if (!hasRequiredRole) {
            return (
              <div className="min-h-screen bg-base-200 flex items-center justify-center">
                <div className="card w-full max-w-md bg-base-100 shadow-xl">
                  <div className="card-body text-center">
                    <h2 className="card-title text-error justify-center">
                      Access Denied
                    </h2>
                    <p className="text-base-content/70">
                      You don't have permission to access this page.
                    </p>
                    <p className="text-sm text-base-content/50">
                      Required role: {requireRole}
                    </p>
                    <div className="card-actions justify-center mt-4">
                      <button
                        className="btn btn-primary"
                        onClick={() => authActions.signOut()}
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            );
          }
        }

        // User is authenticated and has required permissions
        return <>{children}</>;
      }}
    </Resolves>
  );
};

// Helper function to check user role
const checkUserRole = (user: any, requiredRole: string): boolean => {
  if (!user || user.status !== 'active') return false;
  
  const roleHierarchy = {
    user: 1,
    admin: 2,
  };
  
  const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
  
  return userLevel >= requiredLevel;
};

export default AuthGuard;
