import React, { useState } from 'react';
import { useResolves } from '@pumped-fn/react';
import { app } from '../../pumped.auth';
import { z } from 'zod';

// Login form validation schema
const LoginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof LoginSchema>;

interface LoginPageProps {
  onSuccess?: () => void;
  onSwitchToRegister?: () => void;
}

const LoginPage: React.FC<LoginPageProps> = ({ onSuccess, onSwitchToRegister }) => {
  const [authState, authActions] = useResolves(app.state.reactive, app.actions);
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });
  const [validationErrors, setValidationErrors] = useState<Partial<LoginFormData>>({});

  const handleInputChange = (field: keyof LoginFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: undefined }));
    }
    // Clear auth error
    if (authState.error) {
      authActions.clearError();
    }
  };

  const validateForm = (): boolean => {
    try {
      LoginSchema.parse(formData);
      setValidationErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Partial<LoginFormData> = {};
        error.errors.forEach(err => {
          if (err.path[0]) {
            errors[err.path[0] as keyof LoginFormData] = err.message;
          }
        });
        setValidationErrors(errors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await authActions.signIn(formData.email, formData.password);
      onSuccess?.();
    } catch (error) {
      // Error is handled by auth state
    }
  };

  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
      <div className="card w-full max-w-md bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title text-2xl font-bold text-center mb-6">
            Welcome Back
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Email Field */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Email</span>
              </label>
              <input
                type="email"
                placeholder="Enter your email"
                className={`input input-bordered w-full ${
                  validationErrors.email ? 'input-error' : ''
                }`}
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={authState.loading}
              />
              {validationErrors.email && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {validationErrors.email}
                  </span>
                </label>
              )}
            </div>

            {/* Password Field */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Password</span>
              </label>
              <input
                type="password"
                placeholder="Enter your password"
                className={`input input-bordered w-full ${
                  validationErrors.password ? 'input-error' : ''
                }`}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                disabled={authState.loading}
              />
              {validationErrors.password && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {validationErrors.password}
                  </span>
                </label>
              )}
            </div>

            {/* Auth Error */}
            {authState.error && (
              <div className="alert alert-error">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{authState.error}</span>
              </div>
            )}

            {/* Submit Button */}
            <div className="form-control mt-6">
              <button
                type="submit"
                className={`btn btn-primary w-full ${authState.loading ? 'loading' : ''}`}
                disabled={authState.loading}
              >
                {authState.loading ? 'Signing In...' : 'Sign In'}
              </button>
            </div>
          </form>

          {/* Switch to Register */}
          {onSwitchToRegister && (
            <div className="text-center mt-4">
              <p className="text-sm">
                Don't have an account?{' '}
                <button
                  type="button"
                  className="link link-primary"
                  onClick={onSwitchToRegister}
                  disabled={authState.loading}
                >
                  Sign up here
                </button>
              </p>
            </div>
          )}

          {/* Demo Credentials */}
          <div className="divider">Demo Credentials</div>
          <div className="bg-base-200 p-3 rounded-lg text-sm">
            <p className="font-semibold mb-1">Admin Account:</p>
            <p>Email: <EMAIL></p>
            <p>Password: admin123</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
