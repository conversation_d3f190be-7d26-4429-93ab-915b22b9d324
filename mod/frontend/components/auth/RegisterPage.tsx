import React, { useState } from 'react';
import { Resolves } from '@pumped-fn/react';
import { app } from '../../pumped.auth';
import { z } from 'zod';

// Registration form validation schema
const RegisterSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type RegisterFormData = z.infer<typeof RegisterSchema>;

interface RegisterPageProps {
  onSuccess?: () => void;
  onSwitchToLogin?: () => void;
}

const RegisterPage: React.FC<RegisterPageProps> = ({ onSuccess, onSwitchToLogin }) => {
  const [formData, setFormData] = useState<RegisterFormData>({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [validationErrors, setValidationErrors] = useState<Partial<RegisterFormData>>({});

  return (
    <Resolves e={[app.state.reactive, app.actions]}>
      {([authState, authActions]) => {

        const handleInputChange = (field: keyof RegisterFormData, value: string) => {
          setFormData(prev => ({ ...prev, [field]: value }));
          // Clear validation error when user starts typing
          if (validationErrors[field]) {
            setValidationErrors(prev => ({ ...prev, [field]: undefined }));
          }
          // Clear auth error
          if (authState.error) {
            authActions.clearError();
          }
        };

        const validateForm = (): boolean => {
          try {
            RegisterSchema.parse(formData);
            setValidationErrors({});
            return true;
          } catch (error) {
            if (error instanceof z.ZodError) {
              const errors: Partial<RegisterFormData> = {};
              error.errors.forEach(err => {
                if (err.path[0]) {
                  errors[err.path[0] as keyof RegisterFormData] = err.message;
                }
              });
              setValidationErrors(errors);
            }
            return false;
          }
        };

        const handleSubmit = async (e: React.FormEvent) => {
          e.preventDefault();

          if (!validateForm()) {
            return;
          }

          try {
            await authActions.signUp(formData.email, formData.password, formData.name);
            onSuccess?.();
          } catch (error) {
            // Error is handled by auth state
          }
        };

        return (
          <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
      <div className="card w-full max-w-md bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title text-2xl font-bold text-center mb-6">
            Create Account
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Name Field */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Full Name</span>
              </label>
              <input
                type="text"
                placeholder="Enter your full name"
                className={`input input-bordered w-full ${
                  validationErrors.name ? 'input-error' : ''
                }`}
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                disabled={authState.loading}
              />
              {validationErrors.name && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {validationErrors.name}
                  </span>
                </label>
              )}
            </div>

            {/* Email Field */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Email</span>
              </label>
              <input
                type="email"
                placeholder="Enter your email"
                className={`input input-bordered w-full ${
                  validationErrors.email ? 'input-error' : ''
                }`}
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={authState.loading}
              />
              {validationErrors.email && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {validationErrors.email}
                  </span>
                </label>
              )}
            </div>

            {/* Password Field */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Password</span>
              </label>
              <input
                type="password"
                placeholder="Enter your password"
                className={`input input-bordered w-full ${
                  validationErrors.password ? 'input-error' : ''
                }`}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                disabled={authState.loading}
              />
              {validationErrors.password && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {validationErrors.password}
                  </span>
                </label>
              )}
            </div>

            {/* Confirm Password Field */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Confirm Password</span>
              </label>
              <input
                type="password"
                placeholder="Confirm your password"
                className={`input input-bordered w-full ${
                  validationErrors.confirmPassword ? 'input-error' : ''
                }`}
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                disabled={authState.loading}
              />
              {validationErrors.confirmPassword && (
                <label className="label">
                  <span className="label-text-alt text-error">
                    {validationErrors.confirmPassword}
                  </span>
                </label>
              )}
            </div>

            {/* Auth Error */}
            {authState.error && (
              <div className="alert alert-error">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{authState.error}</span>
              </div>
            )}

            {/* Submit Button */}
            <div className="form-control mt-6">
              <button
                type="submit"
                className={`btn btn-primary w-full ${authState.loading ? 'loading' : ''}`}
                disabled={authState.loading}
              >
                {authState.loading ? 'Creating Account...' : 'Create Account'}
              </button>
            </div>
          </form>

          {/* Switch to Login */}
          {onSwitchToLogin && (
            <div className="text-center mt-4">
              <p className="text-sm">
                Already have an account?{' '}
                <button
                  type="button"
                  className="link link-primary"
                  onClick={onSwitchToLogin}
                  disabled={authState.loading}
                >
                  Sign in here
                </button>
              </p>
            </div>
          )}
          </div>
        </div>
      </div>
    );
      }}
    </Resolves>
  );
};

export default RegisterPage;
