import { provide, derive } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";

// Auth state types
export interface AuthUser {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string;
  role: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthSession {
  id: string;
  userId: string;
  expiresAt: Date;
  token: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface AuthState {
  user: AuthUser | null;
  session: AuthSession | null;
  loading: boolean;
  error: string | null;
}

// Initial auth state - start with loading: true to trigger initialization
const initialAuthState: AuthState = {
  user: null,
  session: null,
  loading: true, // Start with loading to show spinner initially
  error: null,
};

// Auth state provider
export const authState = provide(() => initialAuthState);

// Auto-initialization action
export const initializeAuthAction = derive(
  [authState.static],
  ([stateCtl]) => {
    return {
      initialize: async () => {
        const currentState = stateCtl.get();

        // Only initialize if we're in loading state and have no user/session
        if (currentState.loading && !currentState.user && !currentState.session) {
          console.log('[AUTH] Starting auto-initialization...');

          try {
            const response = await fetch('/api/session', { credentials: 'include' });

            if (!response.ok) {
              throw new Error(`Session check failed: ${response.status}`);
            }

            const sessionData = await response.json();

            stateCtl.update((s: any) => ({
              ...s,
              user: sessionData.user,
              session: sessionData.session,
              loading: false,
            }));

            console.log('[AUTH] Auto-initialized:', sessionData.user ? 'authenticated' : 'not authenticated');
          } catch (error) {
            console.error('[AUTH] Auto-initialization failed:', error);
            stateCtl.update((s: any) => ({
              ...s,
              loading: false,
              error: null, // Don't show error for failed initialization
            }));
          }
        }
      }
    };
  }
);

// Derived auth status
export const isAuthenticated = derive(
  [authState.reactive],
  ([state]) => {
    return state.user !== null && state.session !== null;
  }
);

export const isAdmin = derive(
  [authState.reactive],
  ([state]) => {
    return state.user?.role === "admin";
  }
);

export const currentUser = derive(
  [authState.reactive],
  ([state]) => {
    return state.user;
  }
);

// Auth API helper
const authApiCall = async (endpoint: string, options: RequestInit = {}) => {
  const response = await fetch(`/api/auth${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    credentials: 'include', // Include cookies for session
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(error || `HTTP ${response.status}`);
  }

  return response.json();
};

// Auth actions
export const authActions = derive(
  [authState.static, logger('auth')],
  ([stateCtl, logger]) => ({

    // Sign in with email and password
    signIn: async (email: string, password: string) => {
      try {
        stateCtl.update(state => ({ ...state, loading: true, error: null }));
        
        const result = await authApiCall('/sign-in', {
          method: 'POST',
          body: JSON.stringify({ email, password }),
        });

        stateCtl.update(state => ({
          ...state,
          user: result.user,
          session: result.session,
          loading: false,
        }));

        logger.info('User signed in:', result.user.email);
        return result;
      } catch (error) {
        logger.error('Sign in failed:', error);
        stateCtl.update(state => ({
          ...state,
          loading: false,
          error: error instanceof Error ? error.message : 'Sign in failed',
        }));
        throw error;
      }
    },

    // Sign up with email, password, and name
    signUp: async (email: string, password: string, name: string) => {
      try {
        stateCtl.update(state => ({ ...state, loading: true, error: null }));
        
        const result = await authApiCall('/sign-up', {
          method: 'POST',
          body: JSON.stringify({ email, password, name }),
        });

        stateCtl.update(state => ({
          ...state,
          user: result.user,
          session: result.session,
          loading: false,
        }));

        logger.info('User signed up:', result.user.email);
        return result;
      } catch (error) {
        logger.error('Sign up failed:', error);
        stateCtl.update(state => ({
          ...state,
          loading: false,
          error: error instanceof Error ? error.message : 'Sign up failed',
        }));
        throw error;
      }
    },

    // Sign out
    signOut: async () => {
      try {
        stateCtl.update(state => ({ ...state, loading: true, error: null }));
        
        await authApiCall('/sign-out', {
          method: 'POST',
        });

        stateCtl.update(state => ({
          ...state,
          user: null,
          session: null,
          loading: false,
        }));

        logger.info('User signed out');
      } catch (error) {
        logger.error('Sign out failed:', error);
        stateCtl.update(state => ({
          ...state,
          loading: false,
          error: error instanceof Error ? error.message : 'Sign out failed',
        }));
        throw error;
      }
    },

    // Clear error
    clearError: () => {
      stateCtl.update(state => ({ ...state, error: null }));
    },
  })
);

// Export unified auth object following the app pattern
export const app = {
  state: authState,
  isAuthenticated,
  isAdmin,
  currentUser,
  actions: authActions,
  initializeAction: initializeAuthAction,
};

// Also export as auth for backward compatibility
export const auth = app;
