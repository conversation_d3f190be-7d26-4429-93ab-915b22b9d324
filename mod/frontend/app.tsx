import { Suspense } from 'react';
import AuthApp from './components/auth/AuthApp';
import { ScopeProvider } from "@pumped-fn/react"

function App() {
  console.log('[APP] Main App component rendering');
  return (
    <ScopeProvider>
      <Suspense fallback={
        <div className="min-h-screen bg-base-200 flex items-center justify-center">
          <div className="text-center">
            <div className="loading loading-spinner loading-lg"></div>
            <p className="mt-4 text-base-content">Loading Tini Melon...</p>
          </div>
        </div>
      }>
        {/* Use the new AuthApp which includes authentication */}
        <AuthApp />
      </Suspense>
    </ScopeProvider>
  );
}

export { App }
