import { derive, provide } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { getServerConfig } from "@/base/backend/config";
import { serve } from "bun";

import frontend from "@/mod/frontend/index.html";
import { userManagementRoutes } from "@/mod/backend/user-management-routes";
import { initializeAuth } from "@/mod/backend/seed-admin";

const serverConfig = provide(() => getServerConfig());

export default derive(
  [serverConfig, logger('dev-server')],
  async ([config, sLogger], ctl) => {
    sLogger.info(`Starting development server on ${config.host}:${config.port}...`);

    // Initialize authentication system
    try {
      await initializeAuth();
      sLogger.info('Authentication system initialized');
    } catch (error) {
      sLogger.error('Failed to initialize authentication:', error);
    }

    const server = serve({
      port: config.port,
      hostname: config.host,
      development: true,
      // Use custom fetch handler instead of routes
      async fetch(req) {
        const url = new URL(req.url);

        // Handle CORS preflight requests
        if (req.method === 'OPTIONS') {
          return new Response(null, {
            status: 200,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            },
          });
        }

        // Handle auth routes
        if (url.pathname.startsWith('/api/auth/')) {
          const { authHandler } = await import('@/mod/backend/auth-routes');
          return await authHandler(req);
        }

        // Handle session route
        if (url.pathname === '/api/session') {
          const { getSessionHandler } = await import('@/mod/backend/auth-routes');
          return await getSessionHandler(req);
        }

        // Handle user management routes (simplified)
        if (url.pathname.startsWith('/api/users')) {
          // For now, just import and use the routes directly
          const routes = userManagementRoutes as any;
          const handler = routes[url.pathname]?.[req.method];
          if (handler) {
            return await handler(req);
          }
        }

        // Handle root route
        if (url.pathname === '/') {
          return frontend(req);
        }

        // Default 404
        return new Response("Not Found", { status: 404 });
      }
    });

    ctl.cleanup(async () => {
      sLogger.info("Shutting down development server...");
      await server.stop();
    });

    sLogger.info(`🚀 Development server running at http://${config.host}:${config.port}`);
    sLogger.info("📱 User Management API available at /api/users");
    sLogger.info("🔐 Authentication API available at /api/auth/*");

    await new Promise<void>(() => {});
  }
)


