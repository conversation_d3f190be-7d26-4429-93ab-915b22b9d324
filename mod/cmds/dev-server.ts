import { derive, provide } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { getServerConfig } from "@/base/backend/config";
import { serve } from "bun";

import frontend from "@/mod/frontend/index.html";
import { userManagementRoutes } from "@/mod/backend/user-management-routes";
import { authRoutes } from "@/mod/backend/auth-routes";
import { initializeAuth } from "@/mod/backend/seed-admin";

const serverConfig = provide(() => getServerConfig());

export default derive(
  [serverConfig, logger('dev-server')],
  async ([config, sLogger], ctl) => {
    sLogger.info(`Starting development server on ${config.host}:${config.port}...`);

    // Initialize authentication system
    try {
      await initializeAuth();
      sLogger.info('Authentication system initialized');
    } catch (error) {
      sLogger.error('Failed to initialize authentication:', error);
    }

    const server = serve({
      port: config.port,
      hostname: config.host,
      development: true,
      routes: {
        "/": frontend,
        ...userManagementRoutes,
        ...authRoutes
      },
      // Add CORS headers for development
      fetch(req, server) {
        // Handle CORS preflight requests
        if (req.method === 'OPTIONS') {
          return new Response(null, {
            status: 200,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            },
          });
        }

        // This will be handled by the routes above
        return new Response("Not Found", { status: 404 });
      }
    });

    ctl.cleanup(async () => {
      sLogger.info("Shutting down development server...");
      await server.stop();
    });

    sLogger.info(`🚀 Development server running at http://${config.host}:${config.port}`);
    sLogger.info("📱 User Management API available at /api/users");
    sLogger.info("🔐 Authentication API available at /api/auth/*");

    await new Promise<void>(() => {});
  }
)


