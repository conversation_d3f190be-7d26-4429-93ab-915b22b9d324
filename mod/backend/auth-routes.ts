import { auth } from "./auth";

// Simple console logger for auth routes (to avoid pumped-fn context issues)
const authLogger = {
  debug: (msg: string, ...args: any[]) => console.log(`[AUTH ROUTES] ${msg}`, ...args),
  error: (msg: string, ...args: any[]) => console.error(`[AUTH ROUTES] ${msg}`, ...args),
};

/**
 * Better Auth route handler
 * Handles all /api/auth/* routes
 */
export const authHandler = async (request: Request): Promise<Response> => {
  try {
    authLogger.debug(`Auth request: ${request.method} ${request.url}`);

    // Log request details
    const url = new URL(request.url);
    authLogger.debug(`Auth path: ${url.pathname}`);
    authLogger.debug(`Auth headers: ${JSON.stringify(Object.fromEntries(request.headers.entries()))}`);

    // Better Auth handles all authentication routes
    const response = await auth.handler(request);

    authLogger.debug(`Auth response: ${response.status} ${response.statusText}`);

    // Log response body for debugging
    const responseText = await response.text();
    authLogger.debug(`Auth response body: ${responseText}`);

    // Return a new response with the same content
    return new Response(responseText, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });
  } catch (error) {
    authLogger.error('Error in auth handler:', error);
    return new Response(
      JSON.stringify({
        error: 'Authentication error',
        details: error instanceof Error ? error.message : String(error)
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

/**
 * Get current session endpoint
 */
export const getSessionHandler = async (request: Request): Promise<Response> => {
  try {
    console.log('[SESSION API] Session check requested');
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      console.log('[SESSION API] No session found, returning null');
      return new Response(
        JSON.stringify({ user: null, session: null }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('[SESSION API] Session found:', session.user?.email);
    return new Response(
      JSON.stringify({
        user: session.user,
        session: session.session,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    authLogger.error('Error getting session:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to get session' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

/**
 * Authentication routes for the server
 */
export const authRoutes = {
  // Better Auth wildcard route (works better with Bun's routing)
  "/api/auth/*": {
    GET: authHandler,
    POST: authHandler,
    PUT: authHandler,
    DELETE: authHandler,
    PATCH: authHandler,
  },

  // Custom session endpoint
  "/api/session": {
    GET: getSessionHandler,
  },
};
