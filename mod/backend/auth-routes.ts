import { auth } from "./auth";

// Simple console logger for auth routes (to avoid pumped-fn context issues)
const authLogger = {
  debug: (msg: string, ...args: any[]) => console.log(`[AUTH ROUTES] ${msg}`, ...args),
  error: (msg: string, ...args: any[]) => console.error(`[AUTH ROUTES] ${msg}`, ...args),
};

/**
 * Better Auth route handler
 * Handles all /api/auth/* routes
 */
export const authHandler = async (request: Request): Promise<Response> => {
  try {
    authLogger.debug(`Auth request: ${request.method} ${request.url}`);
    
    // Better Auth handles all authentication routes
    return await auth.handler(request);
  } catch (error) {
    authLogger.error('Error in auth handler:', error);
    return new Response(
      JSON.stringify({ error: 'Authentication error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

/**
 * Get current session endpoint
 */
export const getSessionHandler = async (request: Request): Promise<Response> => {
  try {
    console.log('[SESSION API] Session check requested');
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      console.log('[SESSION API] No session found, returning null');
      return new Response(
        JSON.stringify({ user: null, session: null }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('[SESSION API] Session found:', session.user?.email);
    return new Response(
      JSON.stringify({
        user: session.user,
        session: session.session,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    authLogger.error('Error getting session:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to get session' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

/**
 * Authentication routes for the server
 */
export const authRoutes = {
  // Explicit Better Auth routes (instead of wildcard)
  "/api/auth/sign-in": {
    POST: authHandler,
  },
  "/api/auth/sign-up": {
    POST: authHandler,
  },
  "/api/auth/sign-out": {
    POST: authHandler,
  },
  "/api/auth/session": {
    GET: authHandler,
  },
  "/api/auth/user": {
    GET: authHandler,
  },

  // Custom session endpoint
  "/api/session": {
    GET: getSessionHandler,
  },
};
