import { auth } from "./auth";
import { logger } from "@/base/dual/logger";

const authLogger = logger('auth.routes');

/**
 * Better Auth route handler
 * Handles all /api/auth/* routes
 */
export const authHandler = async (request: Request): Promise<Response> => {
  try {
    authLogger.debug(`Auth request: ${request.method} ${request.url}`);
    
    // Better Auth handles all authentication routes
    return await auth.handler(request);
  } catch (error) {
    authLogger.error('Error in auth handler:', error);
    return new Response(
      JSON.stringify({ error: 'Authentication error' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

/**
 * Get current session endpoint
 */
export const getSessionHandler = async (request: Request): Promise<Response> => {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return new Response(
        JSON.stringify({ user: null, session: null }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({
        user: session.user,
        session: session.session,
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    authLogger.error('Error getting session:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to get session' }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

/**
 * Authentication routes for the server
 */
export const authRoutes = {
  // Better Auth handles all /api/auth/* routes
  "/api/auth/*": {
    GET: authHandler,
    POST: authHandler,
    PUT: authHandler,
    DELETE: authHandler,
    PATCH: authHandler,
  },
  
  // Custom session endpoint
  "/api/session": {
    GET: getSessionHandler,
  },
};
