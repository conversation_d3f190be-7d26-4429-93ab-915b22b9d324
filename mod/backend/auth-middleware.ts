import { auth, hasPermission, isActiveUser } from "./auth";

// Simple console logger for auth middleware (to avoid pumped-fn context issues)
const authLogger = {
  debug: (msg: string, ...args: any[]) => console.log(`[AUTH MIDDLEWARE] ${msg}`, ...args),
  warn: (msg: string, ...args: any[]) => console.warn(`[AUTH MIDDLEWARE] ${msg}`, ...args),
  error: (msg: string, ...args: any[]) => console.error(`[AUTH MIDDLEWARE] ${msg}`, ...args),
};

export interface AuthenticatedRequest extends Request {
  user?: any;
  session?: any;
}

/**
 * Authentication middleware that validates session and adds user to request
 */
export const authMiddleware = async (request: Request): Promise<{ user: any; session: any } | null> => {
  try {
    // Get session from Better Auth
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      authLogger.debug('No session found in request');
      return null;
    }

    // Check if user is active
    if (!isActiveUser(session.user)) {
      authLogger.warn(`Inactive user attempted access: ${session.user.email}`);
      return null;
    }

    authLogger.debug(`Authenticated user: ${session.user.email} (${session.user.role})`);
    return { user: session.user, session: session.session };
  } catch (error) {
    authLogger.error('Error in auth middleware:', error);
    return null;
  }
};

/**
 * Require authentication middleware
 */
export const requireAuth = (handler: (req: AuthenticatedRequest) => Promise<Response>) => {
  return async (request: Request): Promise<Response> => {
    const authResult = await authMiddleware(request);
    
    if (!authResult) {
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Add auth info to request
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = authResult.user;
    authenticatedRequest.session = authResult.session;

    return handler(authenticatedRequest);
  };
};

/**
 * Require specific role middleware
 */
export const requireRole = (role: string, handler: (req: AuthenticatedRequest) => Promise<Response>) => {
  return async (request: Request): Promise<Response> => {
    const authResult = await authMiddleware(request);
    
    if (!authResult) {
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!hasPermission(authResult.user, role)) {
      authLogger.warn(`Access denied for user ${authResult.user.email} to role ${role}`);
      return new Response(
        JSON.stringify({ error: 'Insufficient permissions' }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Add auth info to request
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = authResult.user;
    authenticatedRequest.session = authResult.session;

    return handler(authenticatedRequest);
  };
};

/**
 * Optional authentication middleware (doesn't fail if no auth)
 */
export const optionalAuth = (handler: (req: AuthenticatedRequest) => Promise<Response>) => {
  return async (request: Request): Promise<Response> => {
    const authResult = await authMiddleware(request);
    
    // Add auth info to request (may be null)
    const authenticatedRequest = request as AuthenticatedRequest;
    if (authResult) {
      authenticatedRequest.user = authResult.user;
      authenticatedRequest.session = authResult.session;
    }

    return handler(authenticatedRequest);
  };
};
