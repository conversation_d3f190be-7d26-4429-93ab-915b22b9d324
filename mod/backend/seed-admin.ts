import { auth } from "./auth";
import { logger } from "@/base/dual/logger";

const seedLogger = logger('auth.seed');

/**
 * Default admin user configuration
 */
const DEFAULT_ADMIN = {
  email: "<EMAIL>",
  password: "admin123", // Should be changed on first login
  name: "Admin User",
  role: "admin",
  status: "active",
};

/**
 * Create default admin user if it doesn't exist
 */
export const seedDefaultAdmin = async (): Promise<void> => {
  try {
    seedLogger.info('Checking for default admin user...');

    // Check if admin user already exists
    const existingAdmin = await auth.api.listUsers({
      query: {
        email: DEFAULT_ADMIN.email,
      },
    });

    if (existingAdmin && existingAdmin.length > 0) {
      seedLogger.info('Default admin user already exists');
      return;
    }

    seedLogger.info('Creating default admin user...');

    // Create admin user using Better Auth
    const adminUser = await auth.api.signUp.email({
      body: {
        email: DEFAULT_ADMIN.email,
        password: DEFAULT_ADMIN.password,
        name: DEFAULT_ADMIN.name,
        role: DEFAULT_ADMIN.role,
        status: DEFAULT_ADMIN.status,
      },
    });

    if (adminUser) {
      seedLogger.info(`Default admin user created successfully: ${DEFAULT_ADMIN.email}`);
      seedLogger.warn(`Default password is: ${DEFAULT_ADMIN.password} - Please change this immediately!`);
    } else {
      seedLogger.error('Failed to create default admin user');
    }
  } catch (error) {
    seedLogger.error('Error seeding default admin user:', error);
    throw error;
  }
};

/**
 * Initialize authentication system
 * This should be called during application startup
 */
export const initializeAuth = async (): Promise<void> => {
  try {
    seedLogger.info('Initializing authentication system...');
    
    // Seed default admin user
    await seedDefaultAdmin();
    
    seedLogger.info('Authentication system initialized successfully');
  } catch (error) {
    seedLogger.error('Failed to initialize authentication system:', error);
    throw error;
  }
};
