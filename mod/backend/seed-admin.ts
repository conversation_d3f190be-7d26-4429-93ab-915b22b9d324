import { auth } from "./auth";

// Simple console logger for seeding (to avoid pumped-fn context issues)
const seedLogger = {
  info: (msg: string, ...args: any[]) => console.log(`[AUTH SEED] ${msg}`, ...args),
  warn: (msg: string, ...args: any[]) => console.warn(`[AUTH SEED] ${msg}`, ...args),
  error: (msg: string, ...args: any[]) => console.error(`[AUTH SEED] ${msg}`, ...args),
};

/**
 * Default admin user configuration
 */
const DEFAULT_ADMIN = {
  email: "<EMAIL>",
  password: "admin123", // Should be changed on first login
  name: "Admin User",
  role: "admin",
  status: "active",
};

/**
 * Create default admin user if it doesn't exist
 */
export const seedDefaultAdmin = async (): Promise<void> => {
  try {
    seedLogger.info('Checking for default admin user...');

    // For now, we'll create a simple seeding mechanism
    // In a real implementation, you would check the database directly
    seedLogger.info('Creating default admin user...');

    // Create a mock request to simulate user creation
    const mockRequest = new Request('http://localhost:3000/api/auth/sign-up', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: DEFAULT_ADMIN.email,
        password: DEFAULT_ADMIN.password,
        name: DEFAULT_ADMIN.name,
        role: DEFAULT_ADMIN.role,
        status: DEFAULT_ADMIN.status,
      }),
    });

    try {
      const response = await auth.handler(mockRequest);
      if (response.ok) {
        seedLogger.info(`Default admin user created successfully: ${DEFAULT_ADMIN.email}`);
        seedLogger.warn(`Default password is: ${DEFAULT_ADMIN.password} - Please change this immediately!`);
      } else {
        const errorText = await response.text();
        if (errorText.includes('already exists') || errorText.includes('duplicate')) {
          seedLogger.info('Default admin user already exists');
        } else {
          seedLogger.error('Failed to create default admin user:', errorText);
        }
      }
    } catch (createError) {
      seedLogger.warn('Admin user might already exist or creation failed:', createError);
    }
  } catch (error) {
    seedLogger.error('Error seeding default admin user:', error);
    // Don't throw error to prevent server startup failure
  }
};

/**
 * Initialize authentication system
 * This should be called during application startup
 */
export const initializeAuth = async (): Promise<void> => {
  try {
    seedLogger.info('Initializing authentication system...');

    // Seed default admin user
    await seedDefaultAdmin();

    seedLogger.info('Authentication system initialized successfully');
  } catch (error) {
    seedLogger.error('Failed to initialize authentication system:', error);
    throw error;
  }
};
