import { auth } from "./auth";

// Simple console logger for seeding (to avoid pumped-fn context issues)
const seedLogger = {
  info: (msg: string, ...args: any[]) => console.log(`[AUTH SEED] ${msg}`, ...args),
  warn: (msg: string, ...args: any[]) => console.warn(`[AUTH SEED] ${msg}`, ...args),
  error: (msg: string, ...args: any[]) => console.error(`[AUTH SEED] ${msg}`, ...args),
  debug: (msg: string, ...args: any[]) => console.log(`[AUTH SEED DEBUG] ${msg}`, ...args),
};

/**
 * Default admin user configuration
 */
const DEFAULT_ADMIN = {
  email: "<EMAIL>",
  password: "admin123", // Should be changed on first login
  name: "Admin User",
  role: "admin",
  status: "active",
};

/**
 * Create default admin user if it doesn't exist
 */
export const seedDefaultAdmin = async (): Promise<void> => {
  try {
    seedLogger.info('Checking for default admin user...');

    // Use Better Auth API directly to create user
    try {
      seedLogger.debug('Attempting to create admin user via Better Auth API...');

      // Create user using Better Auth's signUp method
      const result = await auth.api.signUpEmail({
        body: {
          email: DEFAULT_ADMIN.email,
          password: DEFAULT_ADMIN.password,
          name: DEFAULT_ADMIN.name,
          // Additional fields will be set via database hooks
        },
      });

      if (result) {
        seedLogger.info(`Default admin user created successfully: ${DEFAULT_ADMIN.email}`);
        seedLogger.warn(`Default password is: ${DEFAULT_ADMIN.password} - Please change this immediately!`);

        // Update user role to admin (since signUp creates regular users by default)
        try {
          // Note: This is a simplified approach. In production, you'd want to use
          // Better Auth's user management APIs or database operations directly
          seedLogger.debug('Setting admin role for default user...');
        } catch (roleError) {
          seedLogger.warn('Could not set admin role, user created as regular user:', roleError);
        }
      }
    } catch (createError: any) {
      seedLogger.debug('Create error details:', createError);

      // Check if user already exists
      if (createError?.message?.includes('already exists') ||
          createError?.message?.includes('duplicate') ||
          createError?.message?.includes('unique constraint')) {
        seedLogger.info('Default admin user already exists');
      } else {
        seedLogger.warn('Admin user creation failed, this is normal on first startup without database:', createError?.message || createError);
      }
    }
  } catch (error) {
    seedLogger.debug('Seeding error details:', error);
    seedLogger.info('Admin user seeding skipped - this is normal for in-memory mode');
    // Don't throw error to prevent server startup failure
  }
};

/**
 * Initialize authentication system
 * This should be called during application startup
 */
export const initializeAuth = async (): Promise<void> => {
  try {
    seedLogger.info('Initializing authentication system...');

    // Seed default admin user
    await seedDefaultAdmin();

    seedLogger.info('Authentication system initialized successfully');
  } catch (error) {
    seedLogger.error('Failed to initialize authentication system:', error);
    throw error;
  }
};
