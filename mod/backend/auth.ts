import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { drizzle } from "drizzle-orm/node-postgres";
import { getDbConfig } from "@/base/db/config";
import { authTables } from "./auth-schema";
import { logger } from "@/base/dual/logger";

// Create database connection for Better Auth
const config = getDbConfig();
const db = drizzle({
  connection: {
    connectionString: `postgres://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`,
    ssl: false
  },
});

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
    schema: authTables,
  }),
  
  // Email and password authentication
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Set to true in production
  },

  // Session configuration
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day (session refresh interval)
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60, // 5 minutes cache
    },
  },

  // User configuration
  user: {
    additionalFields: {
      role: {
        type: "string",
        defaultValue: "user",
      },
      status: {
        type: "string", 
        defaultValue: "active",
      },
    },
  },

  // Database hooks for user management
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          // Set default role and status for new users
          return {
            data: {
              ...user,
              role: user.role || "user",
              status: user.status || "active",
            },
          };
        },
        after: async (user) => {
          // Log user creation
          const authLogger = logger('auth.userCreated');
          authLogger.info(`New user created: ${user.email} with role: ${user.role}`);
        },
      },
    },
  },

  // Advanced security settings
  advanced: {
    useSecureCookies: process.env.NODE_ENV === "production",
    cookiePrefix: "tini_melon",
    defaultCookieAttributes: {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    },
  },

  // Base URL for redirects
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",
});

// Helper functions for role-based authorization
export const isAdmin = (user: any): boolean => {
  return user?.role === "admin";
};

export const isActiveUser = (user: any): boolean => {
  return user?.status === "active";
};

export const hasPermission = (user: any, requiredRole: string): boolean => {
  if (!user || !isActiveUser(user)) return false;
  
  const roleHierarchy = {
    user: 1,
    admin: 2,
  };
  
  const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
  
  return userLevel >= requiredLevel;
};

// Export auth instance and types
export type AuthUser = typeof auth.$Infer.User;
export type AuthSession = typeof auth.$Infer.Session;
