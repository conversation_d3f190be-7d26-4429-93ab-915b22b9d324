# Tini Melon - Full-Stack User Management Application

## Overview

Tini Melon is a modern full-stack application built with Bun, React, and DaisyUI, featuring comprehensive user management capabilities. The application demonstrates a modular architecture using the pumped-fn pattern for state management and dependency injection.

## ✨ Features

### 🎯 **User Management System**
- **Complete CRUD Operations**: Create, read, update, and delete users
- **Real-time Validation**: Form validation with immediate feedback
- **Advanced Filtering**: Search by name/email, filter by role and status
- **Status Management**: Toggle user active/inactive status
- **Responsive Design**: Mobile-first approach with DaisyUI components
- **Notification System**: Toast notifications for user feedback
- **Pagination**: Efficient handling of large user lists

### 📋 **Todo Management**
- Task creation and management
- User assignment and tracking
- Status updates and filtering

### 🎨 **UI/UX Features**
- **DaisyUI Components**: Professional UI with consistent theming
- **Responsive Navigation**: Mobile-friendly navigation with dropdown menus
- **Loading States**: Visual feedback during operations
- **Error Handling**: Comprehensive error management with user-friendly messages
- **Accessibility**: Keyboard navigation and screen reader support

## 🚀 Quick Start

### Prerequisites

- **Bun** (latest version) - [Install Bun](https://bun.sh/docs/installation)
- **Docker & Docker Compose** (for database) - [Install Docker](https://docs.docker.com/get-docker/)
- **Node.js** (for compatibility) - [Install Node.js](https://nodejs.org/)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tini-melon
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Set up environment variables**
   ```bash
   cp .env .env.local  # Copy and modify if needed
   ```

### 🎯 **Development Mode (Recommended for User Management)**

For testing the user management features without database dependencies:

```bash
# Start the development server (no database required)
bun run script dev-server
```

**Access the application:**
- 🌐 **Frontend**: http://localhost:3000
- 📱 **User Management API**: http://localhost:3000/api/users
- 🎨 **Features**: Full user management with in-memory storage

### 🗄️ **Full Stack Mode (With Database)**

For complete functionality including persistent storage:

1. **Start the database services**
   ```bash
   # Start PostgreSQL and Electric services
   task compose:up
   ```

2. **Set up the database schema**
   ```bash
   # Push database schema
   task drizzle:up
   ```

3. **Seed the database (optional)**
   ```bash
   # Add sample data
   bun run script db seed
   ```

4. **Start the full server**
   ```bash
   # Start server with database integration
   bun run script server
   ```

**Access the application:**
- 🌐 **Frontend**: http://localhost:3000
- 🔌 **RPC API**: http://localhost:3000/rpc
- 📊 **Shape API**: http://localhost:3000/shape
- 📱 **User Management API**: http://localhost:3000/api/users

## 📋 Available Commands

### Development Commands
```bash
# Development server (no database)
bun run script dev-server

# Full server with database
bun run script server
```

### Database Commands
```bash
# Start database services
task compose:up

# Stop database services
task compose:down

# Update database schema
task drizzle:up

# Seed database with sample data
bun run script db seed

# Reset database
bun run script db reset
```

### Utility Commands
```bash
# Install dependencies
bun install

# Type checking
bun run tsc --noEmit

# View available tasks
task --list
```

## 🏗️ Architecture

### Base-Mod Pattern

The repository follows a modular architecture with clear separation:

- **`base/`**: Reusable infrastructure and core functionality
- **`mod/`**: Application-specific features and customizations

### Technology Stack

- **Runtime**: Bun (JavaScript runtime and package manager)
- **Frontend**: React 19 with TypeScript
- **UI Framework**: DaisyUI + Tailwind CSS
- **State Management**: pumped-fn (reactive state management)
- **Backend**: Bun server with custom routing
- **Database**: PostgreSQL with Drizzle ORM
- **Real-time**: Electric SQL for live data synchronization

### Key Patterns

- **Pumped-fn Pattern**: Reactive state management with `provide`/`derive`
- **Component Integration**: React components with `@pumped-fn/react`
- **Type Safety**: Full TypeScript with Zod validation
- **Modular Design**: Clear separation of concerns

## 📁 Project Structure

```
tini-melon/
├── base/                          # Core infrastructure
│   ├── backend/                   # Server configuration
│   ├── cmds/                      # Base commands
│   ├── db/                        # Database configuration
│   └── dual/                      # Shared types and utilities
├── mod/                           # Application features
│   ├── backend/                   # API routes and schemas
│   ├── cmds/                      # Application commands
│   ├── dual/                      # Shared application types
│   └── frontend/                  # React components and state
├── docs/                          # Documentation
├── .base/                         # Docker configurations
└── dist/                          # Build output
```

## 🧪 Testing the User Management System

### Manual Testing Checklist

1. **User Creation**
   - ✅ Navigate to User Management tab
   - ✅ Click "Add User" button
   - ✅ Fill form with valid data
   - ✅ Submit and verify success notification
   - ✅ Check user appears in table

2. **Form Validation**
   - ✅ Test required field validation
   - ✅ Test email format validation
   - ✅ Test real-time validation feedback
   - ✅ Test duplicate email handling

3. **User Operations**
   - ✅ Edit existing user
   - ✅ Toggle user status
   - ✅ Search and filter users
   - ✅ Test pagination

### API Testing

```bash
# Test user creation
curl -X POST "http://localhost:3000/api/users" \
  -H "Content-Type: application/json" \
  -d '{"firstName":"John","lastName":"Doe","email":"<EMAIL>","role":"user","status":"active"}'

# Test user listing
curl -X GET "http://localhost:3000/api/users"

# Test user filtering
curl -X GET "http://localhost:3000/api/users?role=admin&status=active"
```

## 📚 Documentation

Detailed documentation is available in the [`.instructions`](.instructions) directory:

1. [**Overview**](.instructions/index.md) - Comprehensive repository overview
2. [**Pumped-fn Pattern**](.instructions/pumped-fn-pattern.md) - State management pattern
3. [**Base/Mod Architecture**](.instructions/base-mod-architecture.md) - Project organization
4. [**Component Integration**](.instructions/component-integration.md) - React integration
5. [**Styling Guide**](.instructions/styling.md) - DaisyUI and Tailwind usage

## 🔧 Configuration

### Environment Variables

```bash
# Server Configuration
SERVER_PORT=3000                  # Application port
SERVER_HOST=0.0.0.0              # Server host

# Database Configuration (for full mode)
POSTGRES_DB=postgres              # Database name
POSTGRES_USER=postgres            # Database user
POSTGRES_PASSWORD=password        # Database password
POSTGRES_PORT=54321              # Database port

# Electric Configuration (for real-time features)
ELECTRIC_HOST=localhost           # Electric host
ELECTRIC_PORT=55432              # Electric port
```

### Docker Services

The application uses Docker Compose for database services:

- **PostgreSQL**: Primary database (port 54321)
- **Electric**: Real-time sync service (port 55432)

## 🚨 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Change port in .env file
   SERVER_PORT=3001
   ```

2. **Database Connection Issues**
   ```bash
   # Ensure Docker services are running
   task compose:up

   # Check service status
   docker compose ps
   ```

3. **Build Errors**
   ```bash
   # Clear cache and reinstall
   rm -rf node_modules bun.lock
   bun install
   ```

### Development Tips

- Use `dev-server` for frontend development without database setup
- Use `server` for full-stack development with persistent data
- Check browser console for frontend errors
- Monitor server logs for backend issues

## 🤝 Contributing

1. Follow the base-mod architecture pattern
2. Use pumped-fn for state management
3. Implement proper TypeScript types
4. Add comprehensive error handling
5. Include responsive design considerations
6. Write clear documentation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

