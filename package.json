{"dependencies": {"@electric-sql/client": "^1.0.4", "@go-task/cli": "^3.43.3", "@pumped-fn/core-next": "^0.5.41", "@pumped-fn/extra": "^0.5.20", "@pumped-fn/react": "^0.5.16", "@types/pg": "^8.15.2", "@types/react": "^19.1.6", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.1.5", "better-auth": "^1.2.8", "bun-plugin-tailwind": "^0.0.15", "consola": "^3.4.2", "daisyui": "^5.0.38", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.0", "drizzle-seed": "^0.3.1", "glob": "^11.0.2", "pg": "^8.16.0", "react": "^19.1.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.1.0", "tailwindcss": "^4.1.7", "ts-morph": "^26.0.0", "ts-to-zod": "^3.15.0", "typescript": "^5.8.3", "zod": "^3.25.34"}, "scripts": {"script": "bun run --bun base/cmd"}, "devDependencies": {"@better-auth/cli": "^1.2.8", "@types/bun": "^1.2.14", "@types/glob": "^8.1.0"}}